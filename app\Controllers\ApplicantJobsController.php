<?php

namespace App\Controllers;

use App\Controllers\BaseController;

class ApplicantJobsController extends BaseController
{
    protected $session;
    protected $positionsModel;
    protected $organizationsModel;
    protected $exercisesModel;
    protected $applicationsModel;
    protected $appxApplicationFilesModel;
    protected $applicantExperiencesModel;
    protected $appxApplicationExperiencesModel;
    protected $applicantEducationModel;
    protected $appxApplicationEducationModel;
    protected $applicantFilesModel;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = session();

        // Initialize models
        $this->positionsModel = new \App\Models\PositionsModel();
        $this->organizationsModel = new \App\Models\DakoiiOrgModel();
        $this->exercisesModel = new \App\Models\ExerciseModel();
        $this->applicationsModel = new \App\Models\AppxApplicationDetailsModel();
        $this->appxApplicationFilesModel = new \App\Models\AppxApplicationFilesModel();
        $this->applicantExperiencesModel = new \App\Models\ApplicantsExperiencesModel();
        $this->appxApplicationExperiencesModel = new \App\Models\AppxApplicationExperiencesModel();
        $this->applicantEducationModel = new \App\Models\ApplicantEducationModel();
        $this->appxApplicationEducationModel = new \App\Models\AppxApplicationEducationModel();
        $this->applicantFilesModel = new \App\Models\ApplicantFilesModel();
    }

    public function index()
    {
        try {
            // Get applicant ID from session
            $applicant_id = session()->get('applicant_id');

            if (!$applicant_id) {
                return redirect()->to('applicant/login')->with('error', 'Please login to view job openings');
            }

            // Get filter parameters
            $selectedOrgId = $this->request->getGet('org_id');
            $searchTerm = $this->request->getGet('search');

            // Load models
            $exerciseModel = new \App\Models\ExerciseModel();
            $orgModel = new \App\Models\DakoiiOrgModel();
            $positionsModel = new \App\Models\PositionsModel();
            $applicantsModel = new \App\Models\ApplicantsModel();

            // Get current applicant's organization information
            $applicant = $applicantsModel->find($applicant_id);
            $applicantOrgId = $applicant['employee_of_org_id'] ?? null;

            // Get all organizations for filter dropdown
            $organizations = $orgModel->where('is_active', 1)->findAll();

            // Get published exercises with organization information
            $exercisesQuery = $exerciseModel->select('
                exercises.*,
                dakoii_org.org_name,
                dakoii_org.org_code,
                dakoii_org.location_lock_province,
                dakoii_org.logo_path
            ')
            ->join('dakoii_org', 'exercises.org_id = dakoii_org.id', 'left')
            ->where('exercises.status', 'published')
            ->where('dakoii_org.is_active', 1);

            // Apply internal/external filtering logic
            if ($applicantOrgId) {
                // Applicant is an employee of an organization
                // Show external exercises + internal exercises from their organization
                $exercisesQuery->groupStart()
                    ->where('exercises.is_internal', 0) // External exercises (visible to all)
                    ->orGroupStart()
                        ->where('exercises.is_internal', 1) // Internal exercises
                        ->where('exercises.org_id', $applicantOrgId) // Only from applicant's organization
                    ->groupEnd()
                ->groupEnd();
            } else {
                // Applicant is not an employee of any organization
                // Show only external exercises
                $exercisesQuery->where('exercises.is_internal', 0);
            }

            // Apply organization filter
            if ($selectedOrgId) {
                $exercisesQuery->where('exercises.org_id', $selectedOrgId);
            }

            // Apply search filter
            if ($searchTerm) {
                $exercisesQuery->groupStart()
                    ->like('exercises.exercise_name', $searchTerm)
                    ->orLike('exercises.description', $searchTerm)
                    ->orLike('dakoii_org.org_name', $searchTerm)
                    ->groupEnd();
            }

            $exercises = $exercisesQuery->findAll();

            // Get position counts for each exercise
            $allJobData = [];
            foreach ($exercises as $exercise) {
                // Count positions for this exercise
                $positionCount = $positionsModel->select('COUNT(*) as count')
                    ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                    ->where('positions_groups.exercise_id', $exercise['id'])
                    ->where('positions.status', 'active')
                    ->first();

                $allJobData[] = [
                    'exercise' => $exercise,
                    'organization' => [
                        'id' => $exercise['org_id'],
                        'org_name' => $exercise['org_name'],
                        'org_code' => $exercise['org_code'],
                        'location_lock_province' => $exercise['location_lock_province'],
                        'logo_path' => $exercise['logo_path']
                    ],
                    'position_count' => $positionCount['count'] ?? 0
                ];
            }

            return view('applicant/applicant_job_openings', [
                'title' => 'Job Openings',
                'menu' => 'jobs',
                'jobData' => $allJobData,
                'organizations' => $organizations,
                'selectedOrgId' => $selectedOrgId,
                'searchTerm' => $searchTerm,
                'totalJobs' => count($allJobData),
                'filteredJobs' => count($allJobData)
            ]);

        } catch (\Exception $e) {
            // Log the error
            log_message('error', 'Error loading applicant jobs: ' . $e->getMessage());

            return view('applicant/applicant_job_openings', [
                'title' => 'Job Openings',
                'menu' => 'jobs',
                'jobData' => [],
                'organizations' => [],
                'selectedOrgId' => $selectedOrgId,
                'searchTerm' => $searchTerm,
                'totalJobs' => 0,
                'filteredJobs' => 0,
                'error' => 'Unable to load job openings. Please try again later.'
            ]);
        }
    }

    public function view($exerciseId)
    {
        try {
            // Get applicant ID from session
            $applicant_id = session()->get('applicant_id');

            if (!$applicant_id) {
                return redirect()->to('applicant/login')->with('error', 'Please login to view exercise details');
            }

            // Load models
            $exerciseModel = new \App\Models\ExerciseModel();
            $orgModel = new \App\Models\DakoiiOrgModel();
            $positionsGroupModel = new \App\Models\PositionsGroupModel();
            $positionsModel = new \App\Models\PositionsModel();
            $applicationsModel = new \App\Models\AppxApplicationDetailsModel();
            $applicantsModel = new \App\Models\ApplicantsModel();

            // Get current applicant's organization information
            $applicant = $applicantsModel->find($applicant_id);
            $applicantOrgId = $applicant['employee_of_org_id'] ?? null;

            // Get exercise from database
            $exercise = $exerciseModel
                ->where('id', $exerciseId)
                ->where('status', 'published')
                ->first();

            if (!$exercise) {
                return redirect()->to('/applicant/jobs')->with('error', 'Exercise not found or not published.');
            }

            // Check if applicant has access to this exercise based on internal/external rules
            if ($exercise['is_internal'] == 1) {
                // This is an internal exercise
                if (!$applicantOrgId || $applicantOrgId != $exercise['org_id']) {
                    // Applicant is not an employee of the organization that posted this internal exercise
                    return redirect()->to('/applicant/jobs')->with('error', 'Access denied. This is an internal position for employees of ' . $exercise['org_name'] . ' only.');
                }
            }
            // External exercises (is_internal = 0) are accessible to all applicants

            // Get organization from database
            $organization = $orgModel->find($exercise['org_id']);
            if (!$organization) {
                return redirect()->to('/applicant/jobs')->with('error', 'Organization not found.');
            }

            // Get position groups for this exercise
            $positionGroups = $positionsGroupModel
                ->where('exercise_id', $exerciseId)
                ->orderBy('group_name', 'ASC')
                ->findAll();

            // Get all positions for this exercise with related data
            $allPositions = $positionsModel
                ->select('
                    positions.*,
                    positions_groups.group_name as position_group_name
                ')
                ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                ->where('positions_groups.exercise_id', $exerciseId)
                ->where('positions.status', 'active')
                ->orderBy('positions_groups.group_name', 'ASC')
                ->orderBy('positions.designation', 'ASC')
                ->findAll();

            // Get applied positions for current applicant (if logged in)
            $appliedPositionIds = [];
            if (session()->get('applicant_id')) {
                $appliedPositions = $applicationsModel
                    ->select('position_id')
                    ->where('applicant_id', session()->get('applicant_id'))
                    ->findAll();
                $appliedPositionIds = array_column($appliedPositions, 'position_id');
            }

            // Group positions by their groups
            $groupedPositions = [];
            foreach ($positionGroups as $group) {
                $groupPositions = array_filter($allPositions, function($pos) use ($group) {
                    return $pos['position_group_id'] == $group['id'];
                });

                // Mark applied positions and add has_applied flag
                foreach ($groupPositions as &$position) {
                    $position['has_applied'] = in_array($position['id'], $appliedPositionIds);
                }

                if (!empty($groupPositions)) {
                    $groupedPositions[$group['group_name']] = array_values($groupPositions);
                }
            }

            return view('applicant/applicant_exercise_details', [
                'title' => 'Exercise Details - ' . $exercise['exercise_name'],
                'menu' => 'jobs',
                'exercise' => $exercise,
                'organization' => $organization,
                'positions' => $groupedPositions
            ]);

        } catch (\Exception $e) {
            // Log the error
            log_message('error', 'Error loading exercise details: ' . $e->getMessage());

            return redirect()->to('/applicant/jobs')->with('error', 'Unable to load exercise details. Please try again later.');
        }
    }

    public function position($positionId)
    {
        // Get applicant ID from session
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to view position details');
        }

        // Get position details
        $position = $this->positionsModel
            ->where('id', $positionId)
            ->where('status', 'active')
            ->first();

        if (!$position) {
            return redirect()->to('/applicant/jobs')->with('error', 'Position not found or not active.');
        }

        // Get organization details
        $orgData = $this->organizationsModel->find($position['org_id']);
        if (!$orgData) {
            return redirect()->to('/applicant/jobs')->with('error', 'Organization not found.');
        }

        // Map organization data to expected field names for the view
        $organization = [
            'id' => $orgData['id'],
            'name' => $orgData['org_name'],
            'org_name' => $orgData['org_name'],
            'org_code' => $orgData['org_code'],
            'orglogo' => $orgData['logo_path'],
            'logo_path' => $orgData['logo_path'],
            'addlockprov' => $orgData['location_lock_province'],
            'location_lock_province' => $orgData['location_lock_province'],
            'description' => $orgData['description'] ?? '',
            'postal_address' => $orgData['postal_address'] ?? '',
            'phone_numbers' => $orgData['phone_numbers'] ?? '',
            'email_addresses' => $orgData['email_addresses'] ?? '',
            'is_active' => $orgData['is_active'] ?? 1,
            'is_locationlocked' => $orgData['is_locationlocked'] ?? 0
        ];

        // Get exercise details
        $exercise = $this->exercisesModel
            ->where('id', $position['exercise_id'])
            ->where('status', 'published')
            ->first();

        if (!$exercise) {
            return redirect()->to('/applicant/jobs')->with('error', 'Exercise not found or not published.');
        }

        // Get applicant details
        $applicantModel = new \App\Models\ApplicantsModel();
        $applicant = $applicantModel->find(session()->get('applicant_id'));
        $applicantOrgId = $applicant['employee_of_org_id'] ?? null;

        // Check if applicant has access to this position based on internal/external rules
        if ($exercise['is_internal'] == 1) {
            // This is an internal position
            if (!$applicantOrgId || $applicantOrgId != $exercise['org_id']) {
                // Applicant is not an employee of the organization that posted this internal position
                return redirect()->to('/applicant/jobs')->with('error', 'Access denied. This is an internal position for employees of ' . $organization['org_name'] . ' only.');
            }
        }
        // External positions (is_internal = 0) are accessible to all applicants

        // Check if applicant has already applied for this position
        $existingApplication = $this->applicationsModel
            ->where('applicant_id', session()->get('applicant_id'))
            ->where('position_id', $positionId)
            ->first();

        return view('applicant/applicant_position_details', [
            'title' => $position['designation'],
            'menu' => 'jobs',
            'position' => $position,
            'organization' => $organization,
            'exercise' => $exercise,
            'applicant' => $applicant,
            'has_applied' => !empty($existingApplication),
            'application' => $existingApplication
        ]);
    }

    public function submissionInterface($positionId)
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Load models
        $exerciseModel = new \App\Models\ExerciseModel();
        $orgModel = new \App\Models\DakoiiOrgModel();
        $positionsModel = new \App\Models\PositionsModel();
        $applicationsModel = new \App\Models\AppxApplicationDetailsModel();

        // Get position details
        $position = $positionsModel->find($positionId);
        if (!$position) {
            return redirect()->to('/applicant/jobs')->with('error', 'Position not found.');
        }

        // Get exercise details
        $exercise = $exerciseModel->find($position['exercise_id']);
        if (!$exercise || (isset($exercise['status']) && $exercise['status'] !== 'published')) {
            return redirect()->to('/applicant/jobs')->with('error', 'Exercise not found or not published.');
        }

        // Get organization details
        $organization = $orgModel->find($position['org_id']);

        // Check if already applied
        $existingApplication = $applicationsModel
            ->where('applicant_id', $applicant_id)
            ->where('position_id', $positionId)
            ->first();

        if ($existingApplication) {
            return redirect()->to('/applicant/applications')->with('error', 'You have already applied for this position.');
        }

        return view('applicant/applicant_application_submission', [
            'title' => 'Application Submission - ' . $position['designation'],
            'menu' => 'jobs',
            'position' => $position,
            'organization' => $organization,
            'exercise' => $exercise
        ]);
    }

    public function processApplication($positionId)
    {
        try {
            if (!$this->request->isAJAX()) {
                return $this->response->setJSON(['success' => false, 'message' => 'Invalid request method']);
            }

            $applicant_id = session()->get('applicant_id');

            if (!$applicant_id) {
                return $this->response->setJSON(['success' => false, 'message' => 'Please login to continue']);
            }

            // Load models
            $applicantsModel = new \App\Models\ApplicantsModel();
            $applicantFilesModel = new \App\Models\ApplicantFilesModel();
            $applicantEducationModel = new \App\Models\ApplicantEducationModel();
            $applicantExperiencesModel = new \App\Models\ApplicantsExperiencesModel();

            // Collect all applicant data
            $applicantData = [
                'personal_info' => $applicantsModel->find($applicant_id),
                'files' => $applicantFilesModel->where('applicant_id', $applicant_id)->findAll(),
                'education' => $applicantEducationModel->where('applicant_id', $applicant_id)->findAll(),
                'experiences' => $applicantExperiencesModel->where('applicant_id', $applicant_id)->findAll()
            ];

            // Remove sensitive data
            if (isset($applicantData['personal_info']['password'])) {
                unset($applicantData['personal_info']['password']);
            }
            if (isset($applicantData['personal_info']['activation_token'])) {
                unset($applicantData['personal_info']['activation_token']);
            }

            return $this->response->setJSON([
                'success' => true,
                'data' => $applicantData
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Process application error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error processing application data: ' . $e->getMessage()
            ]);
        }
    }

    public function apply($positionId)
    {
        try {
            if (!$this->request->isAJAX()) {
                log_message('error', 'Job application: Non-AJAX request received');
                return $this->response->setJSON(['success' => false, 'message' => 'Invalid request method']);
            }

            // Check if position exists and is active
            $position = $this->positionsModel
                ->where('id', $positionId)
                ->where('status', 'active')
                ->first();

            if (!$position) {
                log_message('error', "Job application: Position {$positionId} not found or not active");
                return $this->response->setJSON(['success' => false, 'message' => 'Position not found or not active']);
            }

            // Check if already applied
            $existingApplication = $this->applicationsModel
                ->where('applicant_id', session()->get('applicant_id'))
                ->where('position_id', $positionId)
                ->first();

            if ($existingApplication) {
                log_message('error', "Job application: Duplicate application for position {$positionId} by applicant " . session()->get('applicant_id'));
                return $this->response->setJSON(['success' => false, 'message' => 'You have already applied for this position']);
            }

            // Get applicant details
            $applicantModel = new \App\Models\ApplicantsModel();
            $applicant = $applicantModel->find(session()->get('applicant_id'));

            if (!$applicant) {
                log_message('error', "Job application: Applicant " . session()->get('applicant_id') . " not found");
                return $this->response->setJSON(['success' => false, 'message' => 'Applicant information not found']);
            }

            // Generate unique application number
            $applicationNumber = 'APP' . date('Y') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);

            // Prepare application data
            $applicationData = [
                'applicant_id' => session()->get('applicant_id'),
                'position_id' => $positionId,
                'application_number' => $applicationNumber,
                'first_name' => $applicant['first_name'],
                'last_name' => $applicant['last_name'],
                'gender' => $applicant['gender'],
                'date_of_birth' => $applicant['dobirth'],
                'place_of_origin' => $applicant['place_of_origin'],
                'id_photo_path' => $applicant['id_photo_path'],
                'contact_details' => $applicant['contact_details'],
                'location_address' => $applicant['location_address'],
                'id_numbers' => $applicant['id_numbers'],
                'current_employer' => $applicant['current_employer'],
                'current_position' => $applicant['current_position'],
                'current_salary' => $applicant['current_salary'],
                'citizenship' => $applicant['citizenship'],
                'marital_status' => $applicant['marital_status'],
                'date_of_marriage' => $applicant['date_of_marriage'],
                'spouse_employer' => $applicant['spouse_employer'],
                'children' => $applicant['children'],
                'offence_convicted' => $applicant['offence_convicted'],
                'referees' => $applicant['referees'],
                'how_did_you_hear_about_us' => $applicant['how_did_you_hear_about_us'],
                'signature_path' => $applicant['signature_path'],
                'publications' => $applicant['publications'],
                'awards' => $applicant['awards'],
                'application_status' => 'pending',
                'created_by' => session()->get('applicant_id')
            ];

            // Start transaction using model
            $this->applicationsModel->db->transStart();

            try {
                // Insert application using model
                if (!$this->applicationsModel->insert($applicationData)) {
                    $error = $this->applicationsModel->errors();
                    log_message('error', 'Job application: Database insert failed - ' . json_encode($error));
                    throw new \Exception('Database error: ' . json_encode($error));
                }

                $applicationId = $this->applicationsModel->getInsertID();

                // Copy experiences data
                $experiences = $this->applicantExperiencesModel->where('applicant_id', session()->get('applicant_id'))->findAll();
                foreach ($experiences as $experience) {
                    $experienceData = [
                        'application_id' => $applicationId,
                        'applicant_id' => session()->get('applicant_id'),
                        'employer' => $experience['employer'],
                        'employer_contacts_address' => $experience['employer_contacts_address'],
                        'position' => $experience['position'],
                        'date_from' => $experience['date_from'],
                        'date_to' => $experience['date_to'],
                        'achievements' => $experience['achievements'],
                        'work_description' => $experience['work_description'],
                        'created_by' => session()->get('applicant_id')
                    ];

                    if (!$this->appxApplicationExperiencesModel->insert($experienceData)) {
                        throw new \Exception('Failed to copy experience data');
                    }
                }

                // Copy education data
                $educationRecords = $this->applicantEducationModel->where('applicant_id', session()->get('applicant_id'))->findAll();
                foreach ($educationRecords as $education) {
                    $educationData = [
                        'application_id' => $applicationId,
                        'applicant_id' => session()->get('applicant_id'),
                        'institution' => $education['institution'],
                        'course' => $education['course'],
                        'date_from' => $education['date_from'],
                        'date_to' => $education['date_to'],
                        'education_level' => $education['education_level'],
                        'units' => $education['units'],
                        'created_by' => session()->get('applicant_id')
                    ];

                    if (!$this->appxApplicationEducationModel->insert($educationData)) {
                        throw new \Exception('Failed to copy education data');
                    }
                }

                // Copy existing files data
                $existingFiles = $this->applicantFilesModel->where('applicant_id', session()->get('applicant_id'))->findAll();
                foreach ($existingFiles as $file) {
                    $existingFileData = [
                        'application_id' => $applicationId,
                        'applicant_id' => session()->get('applicant_id'),
                        'file_title' => $file['file_title'],
                        'file_description' => $file['file_description'],
                        'file_path' => $file['file_path'],
                        'created_by' => session()->get('applicant_id')
                    ];

                    if (!$this->appxApplicationFilesModel->insert($existingFileData)) {
                        throw new \Exception('Failed to copy existing file data');
                    }
                }

                // Complete transaction
                $this->applicationsModel->db->transComplete();

                if ($this->applicationsModel->db->transStatus() === false) {
                    throw new \Exception('Transaction failed');
                }

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Application submitted successfully'
                ]);

            } catch (\Exception $e) {
                // Rollback transaction using model
                $this->applicationsModel->db->transRollback();
                log_message('error', 'Job application: Transaction failed - ' . $e->getMessage());

                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Error submitting application: ' . $e->getMessage()
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Job application: Unexpected error - ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An unexpected error occurred. Please try again later.'
            ]);
        }
    }

    public function finalSubmission($positionId)
    {
        try {
            $applicant_id = session()->get('applicant_id');

            if (!$applicant_id) {
                return redirect()->to('applicant/login')->with('error', 'Please login to continue');
            }

            // Get profile data from form
            $profileData = $this->request->getPost('profile_data');

            // Debug logging
            log_message('info', 'Final submission started for position: ' . $positionId);
            log_message('info', 'Profile data received: ' . ($profileData ? 'YES' : 'NO'));
            log_message('info', 'Profile data length: ' . strlen($profileData ?? ''));

            if (!$profileData) {
                log_message('error', 'Profile data is missing in final submission');
                return redirect()->back()->with('error', 'Profile data is required for submission');
            }

            // Check if position exists and is active
            $position = $this->positionsModel
                ->where('id', $positionId)
                ->where('status', 'active')
                ->first();

            if (!$position) {
                return redirect()->back()->with('error', 'Position not found or not active');
            }

            // Check if already applied
            $existingApplication = $this->applicationsModel
                ->where('applicant_id', $applicant_id)
                ->where('position_id', $positionId)
                ->first();

            if ($existingApplication) {
                return redirect()->to('/applicant/applications')->with('error', 'You have already applied for this position');
            }

            // Get applicant details
            $applicantModel = new \App\Models\ApplicantsModel();
            $applicant = $applicantModel->find($applicant_id);

            if (!$applicant) {
                return redirect()->back()->with('error', 'Applicant information not found');
            }

            // Generate unique application number
            $applicationNumber = 'APP' . date('Y') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);

            // Start transaction
            $this->applicationsModel->db->transStart();

            try {
                // Use the application number already generated above

                // Create application files directory
                $tempApplicationId = 'temp_' . time() . '_' . $applicant_id;
                $applicationFilesDir = FCPATH . 'uploads/applications/' . $tempApplicationId;
                if (!is_dir($applicationFilesDir)) {
                    mkdir($applicationFilesDir, 0777, true);
                }

                // Copy ID photo if exists
                $newIdPhotoPath = null;
                if (!empty($applicant['id_photo_path'])) {
                    // Remove 'public/' prefix if it exists to get the relative path
                    $relativePath = str_starts_with($applicant['id_photo_path'], 'public/')
                        ? substr($applicant['id_photo_path'], 7)
                        : $applicant['id_photo_path'];

                    $originalIdPhotoPath = FCPATH . $relativePath;
                    log_message('info', 'Attempting to copy ID photo from: ' . $originalIdPhotoPath);

                    if (file_exists($originalIdPhotoPath)) {
                        $photoInfo = pathinfo($originalIdPhotoPath);
                        $newPhotoName = 'id_photo_' . $applicant_id . '_' . time() . '.' . $photoInfo['extension'];
                        $newPhotoFullPath = $applicationFilesDir . '/' . $newPhotoName;

                        if (copy($originalIdPhotoPath, $newPhotoFullPath)) {
                            $newIdPhotoPath = 'public/uploads/applications/' . $tempApplicationId . '/' . $newPhotoName;
                            log_message('info', 'ID photo copied successfully: ' . $originalIdPhotoPath . ' -> ' . $newPhotoFullPath);
                        } else {
                            log_message('error', 'Failed to copy ID photo: ' . $originalIdPhotoPath);
                        }
                    } else {
                        log_message('warning', 'Original ID photo file not found: ' . $originalIdPhotoPath);
                    }
                }

                // Copy signature if exists
                $newSignaturePath = null;
                if (!empty($applicant['signature_path'])) {
                    // Remove 'public/' prefix if it exists to get the relative path
                    $relativePath = str_starts_with($applicant['signature_path'], 'public/')
                        ? substr($applicant['signature_path'], 7)
                        : $applicant['signature_path'];

                    $originalSignaturePath = FCPATH . $relativePath;
                    log_message('info', 'Attempting to copy signature from: ' . $originalSignaturePath);

                    if (file_exists($originalSignaturePath)) {
                        $signatureInfo = pathinfo($originalSignaturePath);
                        $newSignatureName = 'signature_' . $applicant_id . '_' . time() . '.' . $signatureInfo['extension'];
                        $newSignatureFullPath = $applicationFilesDir . '/' . $newSignatureName;

                        if (copy($originalSignaturePath, $newSignatureFullPath)) {
                            $newSignaturePath = 'public/uploads/applications/' . $tempApplicationId . '/' . $newSignatureName;
                            log_message('info', 'Signature copied successfully: ' . $originalSignaturePath . ' -> ' . $newSignatureFullPath);
                        } else {
                            log_message('error', 'Failed to copy signature: ' . $originalSignaturePath);
                        }
                    } else {
                        log_message('warning', 'Original signature file not found: ' . $originalSignaturePath);
                    }
                }

                // Copy applicant data to application details
                $applicationData = [
                    'org_id' => $position['org_id'],
                    'exercise_id' => $position['exercise_id'],
                    'applicant_id' => $applicant_id,
                    'position_id' => $positionId,
                    'application_number' => $applicationNumber,
                    'first_name' => $applicant['first_name'],
                    'last_name' => $applicant['last_name'],
                    'gender' => $applicant['gender'],
                    'date_of_birth' => $applicant['dobirth'],
                    'place_of_origin' => $applicant['place_of_origin'],
                    'id_photo_path' => $newIdPhotoPath,
                    'contact_details' => $applicant['contact_details'],
                    'location_address' => $applicant['location_address'],
                    'id_numbers' => $applicant['id_numbers'],
                    'current_employer' => $applicant['current_employer'],
                    'current_position' => $applicant['current_position'],
                    'current_salary' => $applicant['current_salary'],
                    'citizenship' => $applicant['citizenship'],
                    'marital_status' => $applicant['marital_status'],
                    'date_of_marriage' => $applicant['date_of_marriage'],
                    'spouse_employer' => $applicant['spouse_employer'],
                    'is_public_servant' => $applicant['is_public_servant'],
                    'public_service_file_number' => $applicant['public_service_file_number'],
                    'employee_of_org_id' => $applicant['employee_of_org_id'],
                    'children' => $applicant['children'],
                    'offence_convicted' => $applicant['offence_convicted'],
                    'referees' => $applicant['referees'],
                    'how_did_you_hear_about_us' => $applicant['how_did_you_hear_about_us'],
                    'signature_path' => $newSignaturePath,
                    'publications' => $applicant['publications'],
                    'awards' => $applicant['awards'],
                    'profile_details' => $profileData,
                    'application_status' => 'submitted',
                    'created_by' => $applicant_id,
                    'updated_by' => $applicant_id
                ];

                log_message('info', 'Attempting to insert application record');
                $applicationId = $this->applicationsModel->insert($applicationData);

                if (!$applicationId) {
                    $errors = $this->applicationsModel->errors();
                    log_message('error', 'Failed to create application record. Errors: ' . json_encode($errors));
                    log_message('error', 'Application data: ' . json_encode($applicationData));
                    throw new \Exception('Failed to create application record: ' . json_encode($errors));
                }

                log_message('info', 'Application record created with ID: ' . $applicationId);

                // Rename the temporary directory to use actual application ID
                $finalApplicationFilesDir = FCPATH . 'uploads/applications/' . $applicationId;
                if (is_dir($applicationFilesDir)) {
                    if (rename($applicationFilesDir, $finalApplicationFilesDir)) {
                        log_message('info', 'Application directory renamed: ' . $applicationFilesDir . ' -> ' . $finalApplicationFilesDir);

                        // Update file paths in application data
                        if (!empty($applicationData['id_photo_path'])) {
                            $oldPath = $applicationData['id_photo_path'];
                            $applicationData['id_photo_path'] = str_replace($tempApplicationId, $applicationId, $applicationData['id_photo_path']);
                            log_message('info', 'Updated ID photo path: ' . $oldPath . ' -> ' . $applicationData['id_photo_path']);
                        }
                        if (!empty($applicationData['signature_path'])) {
                            $oldPath = $applicationData['signature_path'];
                            $applicationData['signature_path'] = str_replace($tempApplicationId, $applicationId, $applicationData['signature_path']);
                            log_message('info', 'Updated signature path: ' . $oldPath . ' -> ' . $applicationData['signature_path']);
                        }

                        // Update the application record with correct file paths
                        $updateResult = $this->applicationsModel->update($applicationId, [
                            'id_photo_path' => $applicationData['id_photo_path'],
                            'signature_path' => $applicationData['signature_path']
                        ]);

                        if ($updateResult) {
                            log_message('info', 'Application file paths updated successfully in database');
                        } else {
                            log_message('error', 'Failed to update application file paths in database');
                        }
                    } else {
                        log_message('error', 'Failed to rename application directory');
                    }
                }

                // Copy education records
                $educationModel = new \App\Models\ApplicantEducationModel();
                $appxEducationModel = new \App\Models\AppxApplicationEducationModel();
                $educationRecords = $educationModel->where('applicant_id', $applicant_id)->findAll();

                foreach ($educationRecords as $education) {
                    $educationData = [
                        'application_id' => $applicationId,
                        'applicant_id' => $applicant_id,
                        'institution' => $education['institution'],
                        'course' => $education['course'],
                        'date_from' => $education['date_from'],
                        'date_to' => $education['date_to'],
                        'education_level' => $education['education_level'],
                        'units' => $education['units'],
                        'created_by' => $applicant_id,
                        'updated_by' => $applicant_id
                    ];
                    $appxEducationModel->insert($educationData);
                }

                // Copy experience records
                $experienceModel = new \App\Models\ApplicantsExperiencesModel();
                $appxExperienceModel = new \App\Models\AppxApplicationExperiencesModel();
                $experienceRecords = $experienceModel->where('applicant_id', $applicant_id)->findAll();

                foreach ($experienceRecords as $experience) {
                    $experienceData = [
                        'application_id' => $applicationId,
                        'applicant_id' => $applicant_id,
                        'employer' => $experience['employer'],
                        'employer_contacts_address' => $experience['employer_contacts_address'] ?? null,
                        'position' => $experience['position'],
                        'date_from' => $experience['date_from'],
                        'date_to' => $experience['date_to'],
                        'achievements' => $experience['achievements'] ?? null,
                        'work_description' => $experience['work_description'] ?? null,
                        'created_by' => $applicant_id,
                        'updated_by' => $applicant_id
                    ];
                    $appxExperienceModel->insert($experienceData);
                }

                // Copy file records and physical files
                $filesModel = new \App\Models\ApplicantFilesModel();
                $appxFilesModel = new \App\Models\AppxApplicationFilesModel();
                $fileRecords = $filesModel->where('applicant_id', $applicant_id)->findAll();

                // Use the renamed application files directory
                $applicationFilesDir = $finalApplicationFilesDir;

                foreach ($fileRecords as $file) {
                    $newFilePath = null;

                    // Copy physical file if it exists
                    if (!empty($file['file_path'])) {
                        $originalFilePath = FCPATH . ltrim($file['file_path'], 'public/');

                        if (file_exists($originalFilePath)) {
                            // Generate new filename for application
                            $fileInfo = pathinfo($originalFilePath);
                            $newFileName = 'app_' . $applicationId . '_' . $file['id'] . '_' . time() . '.' . $fileInfo['extension'];
                            $newFileFullPath = $applicationFilesDir . '/' . $newFileName;

                            // Copy the file
                            if (copy($originalFilePath, $newFileFullPath)) {
                                $newFilePath = 'public/uploads/applications/' . $applicationId . '/' . $newFileName;
                                log_message('info', 'File copied successfully: ' . $originalFilePath . ' -> ' . $newFileFullPath);
                            } else {
                                log_message('error', 'Failed to copy file: ' . $originalFilePath . ' -> ' . $newFileFullPath);
                            }
                        } else {
                            log_message('warning', 'Original file not found: ' . $originalFilePath);
                        }
                    }

                    $fileData = [
                        'application_id' => $applicationId,
                        'applicant_id' => $applicant_id,
                        'applicant_file_id' => $file['id'],
                        'file_title' => $file['file_title'],
                        'file_description' => $file['file_description'],
                        'file_path' => $newFilePath,
                        'extracted_texts' => $file['file_extracted_texts'],
                        'created_by' => $applicant_id,
                        'updated_by' => $applicant_id
                    ];
                    $appxFilesModel->insert($fileData);
                }

                // Complete transaction
                $this->applicationsModel->db->transComplete();

                if ($this->applicationsModel->db->transStatus() === false) {
                    throw new \Exception('Transaction failed');
                }

                return redirect()->to('/applicant/applications')->with('success', 'Application submitted successfully! Application Number: ' . $applicationNumber);

            } catch (\Exception $e) {
                $this->applicationsModel->db->transRollback();
                log_message('error', 'Final submission error: ' . $e->getMessage());
                return redirect()->back()->with('error', 'Error submitting application: ' . $e->getMessage());
            }

        } catch (\Exception $e) {
            log_message('error', 'Final submission unexpected error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'An unexpected error occurred. Please try again later.');
        }
    }

    /**
     * Download application file
     *
     * @param string $filename
     * @return mixed
     */
    public function downloadFile($filename)
    {
        // Security check: Verify the file belongs to this applicant
        $file = $this->appxApplicationFilesModel
            ->where('applicant_id', session()->get('applicant_id'))
            ->where('file_path', 'public/uploads/applications/' . $filename)
            ->first();

        if (!$file) {
            return $this->response->setStatusCode(403)->setBody('Access denied');
        }

        $path = FCPATH . 'public/uploads/applications/' . $filename;

        if (!file_exists($path)) {
            return $this->response->setStatusCode(404)->setBody('File not found');
        }

        return $this->response->download($path, null)->setFileName($filename);
    }

    /**
     * Display list of applications for the logged-in applicant
     */
    public function applications()
    {
        // Get applicant ID from session
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to view your applications');
        }

        // Get real applications with related data
        $applicationData = [];
        $applications = $this->applicationsModel->select('
                appx_application_details.*,
                positions.designation,
                positions.location as position_location,
                positions.annual_salary,
                positions.classification,
                dakoii_org.org_name,
                dakoii_org.org_code,
                dakoii_org.location_lock_province,
                exercises.exercise_name,
                exercises.advertisement_no
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->join('dakoii_org', 'appx_application_details.org_id = dakoii_org.id', 'left')
            ->join('exercises', 'appx_application_details.exercise_id = exercises.id', 'left')
            ->where('appx_application_details.applicant_id', $applicant_id)
            ->orderBy('appx_application_details.created_at', 'DESC')
            ->findAll();

        // Format data to match the expected structure in the view
        foreach ($applications as $app) {
            $applicationData[] = [
                'application' => [
                    'id' => $app['id'],
                    'application_number' => $app['application_number'],
                    'application_status' => $app['application_status'] ?? 'pending',
                    'created_at' => $app['created_at'],
                    'updated_at' => $app['updated_at']
                ],
                'position' => [
                    'id' => $app['position_id'],
                    'designation' => $app['designation'] ?? 'Position Not Found',
                    'department' => $app['org_name'] ?? 'Department Not Found',
                    'salary_range' => $app['annual_salary'] ? $app['annual_salary'] : 'Not Specified',
                    'location' => $app['position_location'] ?? 'Location Not Specified',
                    'classification' => $app['classification'] ?? ''
                ],
                'organization' => [
                    'id' => $app['org_id'],
                    'org_name' => $app['org_name'] ?? 'Organization Not Found',
                    'org_code' => $app['org_code'] ?? '',
                    'location_lock_province' => $app['location_lock_province'] ?? ''
                ],
                'exercise' => [
                    'id' => $app['exercise_id'],
                    'exercise_name' => $app['exercise_name'] ?? 'Exercise Not Found',
                    'advertisement_no' => $app['advertisement_no'] ?? ''
                ]
            ];
        }

        return view('applicant/applicant_applications', [
            'title' => 'My Applications',
            'menu' => 'applications',
            'applications' => $applicationData
        ]);
    }
}